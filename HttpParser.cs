using System.Net.Sockets;
using System.Text;

public static class HttpParser
{
    public static async Task<HttpRequest?> ParseAsync(NetworkStream stream, CancellationToken token)
    {
        // We can't use StreamReader here anymore because it might buffer too much data,
        // reading part of the body which we need to handle separately.
        // We will read bytes manually.
        var buffer = new byte[8192]; // 8KB buffer
        var bytesRead = await stream.ReadAsync(buffer, token);
        if (bytesRead == 0) return null;

        var requestData = Encoding.UTF8.GetString(buffer, 0, bytesRead);

        // Find the end of the headers
        var headerEndIndex = requestData.IndexOf("\r\n\r\n");
        if (headerEndIndex == -1) return null;

        var headerSection = requestData.Substring(0, headerEndIndex);
        var lines = headerSection.Split("\r\n");

        string[] requestLineParts = lines[0].Split(' ');
        if (requestLineParts.Length != 3) return null;

        var request = new HttpRequest
        {
            Method = requestLineParts[0],
            Path = requestLineParts[1],
            Version = requestLineParts[2]
        };

        // Parse headers
        for (int i = 1; i < lines.Length; i++)
        {
            var headerParts = lines[i].Split(':', 2);
            if (headerParts.Length == 2)
            {
                request.Headers[headerParts[0].Trim()] = headerParts[1].Trim();
            }
        }

        // Parse body if Content-Length is specified
        if (request.Headers.TryGetValue("Content-Length", out var contentLengthStr) &&
            int.TryParse(contentLengthStr, out int contentLength) &&
            contentLength > 0)
        {
            // Calculate how much of the body we've already read into the buffer
            int bodyStart = headerEndIndex + 4;
            int initialBodyBytesRead = bytesRead - bodyStart;

            var body = new byte[contentLength];
            // Copy the part of the body we already have
            Buffer.BlockCopy(buffer, bodyStart, body, 0, initialBodyBytesRead);

            // Read the rest of the body directly from the stream
            int remainingBytes = contentLength - initialBodyBytesRead;
            if (remainingBytes > 0)
            {
                await stream.ReadExactlyAsync(body, initialBodyBytesRead, remainingBytes, token);
            }

            request.Body = body;
        }

        return request;
    }
}
