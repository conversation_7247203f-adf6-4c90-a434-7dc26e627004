using System.Net;
using System.Net.Sockets;
using System.Text;

public class HttpServer
{
    private readonly int _port;
    private readonly string? _fileDirectory;
    private readonly TcpListener _listener;

    public HttpServer(int port, string? fileDirectory)
    {
        _port = port;
        _fileDirectory = fileDirectory;
        _listener = new TcpListener(IPAddress.Any, _port);
    }

    public async Task StartAsync(CancellationToken token)
    {
        _listener.Start();
        Console.WriteLine($"Listening for connections on port {_port}");

        while (!token.IsCancellationRequested)
        {
            try
            {
                TcpClient client = await _listener.AcceptTcpClientAsync(token);
                Console.WriteLine("Client connected...");
                // Fire-and-forget handler for concurrency
                _ = HandleClientAsync(client, token);
            }
            catch (OperationCanceledException)
            {
                // This exception is thrown by AcceptTcpClientAsync when the token is cancelled.
                // We can safely ignore it and let the loop terminate.
                throw;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error accepting client: {ex.Message}");
            }
        }

        _listener.Stop();
    }

    private async Task HandleClientAsync(TcpClient client, CancellationToken token)
    {
        try
        {
            using (client)
            await using (var stream = client.GetStream())
            {
                // Parse the incoming request
                var request = await HttpParser.ParseAsync(stream, token);

                // If parsing fails or request is invalid, send a Bad Request response
                if (request == null || !request.Headers.ContainsKey("Host"))
                {
                    var badRequestResponse = new HttpResponse { StatusCode = 400, StatusMessage = "Bad Request" };
                    await stream.WriteAsync(badRequestResponse.ToBytes(), token);
                    await stream.FlushAsync(token);
                    return;
                }

                Console.WriteLine($"Received request: {request.Method} {request.Path}");

                // Further Expanded Routing Logic
                HttpResponse response;
                if (request.Path == "/")
                {
                    response = new HttpResponse { StatusCode = 200, StatusMessage = "OK" };
                }
                else if (request.Path.StartsWith("/echo/"))
                {
                    // Extract the content from the path, e.g., "abc" from "/echo/abc"
                    var content = request.Path.Substring(6);
                    var bodyBytes = Encoding.UTF8.GetBytes(content);

                    response = new HttpResponse
                    {
                        StatusCode = 200,
                        StatusMessage = "OK",
                        Body = bodyBytes
                    };
                    response.Headers["Content-Type"] = "text/plain";
                    response.Headers["Content-Length"] = response.Body.Length.ToString();
                }
                else if (request.Path == "/user-agent")
                {
                    var userAgent = request.Headers.GetValueOrDefault("User-Agent", "Unknown");
                    var bodyBytes = Encoding.UTF8.GetBytes(userAgent);

                    response = new HttpResponse
                    {
                        StatusCode = 200,
                        StatusMessage = "OK",
                        Body = bodyBytes
                    };
                    response.Headers["Content-Type"] = "text/plain";
                    response.Headers["Content-Length"] = response.Body.Length.ToString();
                }
                else if (request.Method == "GET" && request.Path.StartsWith("/files/"))
                {
                    if (string.IsNullOrEmpty(_fileDirectory))
                    {
                        response = new HttpResponse { StatusCode = 500, StatusMessage = "Internal Server Error" };
                    }
                    else
                    {
                        var filename = request.Path.Substring(7); // Remove "/files/" prefix
                        var filePath = Path.Combine(_fileDirectory, filename);

                        if (File.Exists(filePath))
                        {
                            var fileBytes = await File.ReadAllBytesAsync(filePath, token);
                            response = new HttpResponse
                            {
                                StatusCode = 200,
                                StatusMessage = "OK",
                                Body = fileBytes
                            };
                            response.Headers["Content-Type"] = "application/octet-stream";
                            response.Headers["Content-Length"] = response.Body.Length.ToString();
                        }
                        else
                        {
                            response = new HttpResponse { StatusCode = 404, StatusMessage = "Not Found" };
                        }
                    }
                }
                else
                {
                    response = new HttpResponse { StatusCode = 404, StatusMessage = "Not Found" };
                }

                byte[] responseBytes = response.ToBytes();
                await stream.WriteAsync(responseBytes, token);
                await stream.FlushAsync(token);
                Console.WriteLine($"Sent {response.StatusCode} response.");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error handling client: {ex.Message}");
        }
        finally
        {
             Console.WriteLine("Client connection closed.");
        }
    }
}
